[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "window-tiler"
version = "1.0.0"
description = "Automatically arrange windows in grids with customizable layouts"
requires-python = ">=3.8"
authors = [
    {name = "Window Tiler Team"}
]
readme = "README.md"
license = {text = "MIT"}

dependencies = [
    "psutil>=7.0.0",
    "pywin32>=310",
    "rich>=14.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
]

[project.scripts]
window-tiler = "src.window_tiler:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 88
