#!/usr/bin/env python3
"""
Window Tiler - Automatically arrange windows in grids

Groups windows by process or type, then tiles them in customizable grids.
Supports minimized windows and multiple monitors.

Requirements: pip install pywin32 psutil rich

Customize window types by modifying WindowTilerConfig class below.
"""

import os
import sys
import math
import argparse
import psutil
import win32api
import win32gui
import win32con
import win32process
from enum import Enum, auto
from rich.console import Console
from rich.prompt import Prompt, Confirm

# Import simple window organizer
try:
    from .window_organizer import (
        export_windows_for_editing,
        import_edited_window_order,
        has_window_order_file,
        cleanup_window_order_file
    )
    ORGANIZER_AVAILABLE = True
except ImportError:
    # Fallback for direct execution
    try:
        from window_organizer import (
            export_windows_for_editing,
            import_edited_window_order,
            has_window_order_file,
            cleanup_window_order_file
        )
        ORGANIZER_AVAILABLE = True
    except ImportError:
        ORGANIZER_AVAILABLE = False

class Config:
    def __init__(self):
        self.types = {
            'chrome.exe': 'Browser', 'firefox.exe': 'Browser', 'msedge.exe': 'Browser',
            'cmd.exe': 'Terminal', 'powershell.exe': 'Terminal', 'windowsterminal.exe': 'Terminal',
            'code.exe': 'Editor', 'notepad.exe': 'Editor', 'sublime_text.exe': 'Editor',
            'devenv.exe': 'IDE', 'pycharm64.exe': 'IDE', 'idea64.exe': 'IDE',
            'explorer.exe': 'Explorer'
        }

        # System processes that should be skipped (non-visible or system UI)
        self.skip = {
            'dwm.exe', 'winlogon.exe', 'csrss.exe', 'svchost.exe', 'lsass.exe',
            'shellexperiencehost.exe', 'searchhost.exe', 'searchapp.exe', 'startmenuexperiencehost.exe',
            'runtimebroker.exe', 'ctfmon.exe', 'dllhost.exe', 'conhost.exe', 'fontdrvhost.exe',
            'applicationframehost.exe', 'windowsinternal.composableshell.experiences.textinput.inputapp.exe',
            'microsoft.windows.cloudexperiencehost.exe', 'microsoft.windows.startmenuexperiencehost.exe',
            'microsoft.windows.shell.experiencehost.exe', 'microsoft.windows.search.exe',
            'microsoft.windows.immersivecontrolpanel.exe', 'microsoft.windows.cortana.exe',
            'microsoft.windows.alarms.exe', 'microsoft.windows.calculator.exe',
            'microsoft.windows.photos.exe', 'microsoft.windows.music.exe', 'microsoft.windows.video.exe',
            'microsoft.windows.maps.exe', 'microsoft.windows.weather.exe', 'microsoft.windows.news.exe',
            'microsoft.windows.sports.exe', 'microsoft.windows.money.exe', 'microsoft.windows.food.exe',
            'microsoft.windows.health.exe', 'microsoft.windows.travel.exe', 'microsoft.windows.people.exe',
            'microsoft.windows.messaging.exe', 'microsoft.windows.phone.exe', 'microsoft.windows.store.exe',
            'microsoft.windows.xbox.exe', 'microsoft.windows.gaming.exe', 'microsoft.windows.movies.exe',
            'microsoft.windows.tv.exe', 'microsoft.windows.radio.exe', 'microsoft.windows.books.exe'
        }

    def get_type(self, process_name):
        return self.types.get(process_name.lower(), 'Other')

    def should_skip(self, process_name):
        return process_name.lower() in self.skip

# ---------------------------------------------------------
# Enumerations & Basic Classes
# ---------------------------------------------------------
class WindowType(Enum):
    BROWSER = auto()
    TERMINAL = auto()
    EXPLORER = auto()
    EDITOR = auto()
    IDE = auto()
    NORMAL = auto()
    UNKNOWN = auto()

class Monitor:
    """Represents a physical display monitor."""

    def __init__(self, handle, info):
        self.handle = handle
        self.is_primary = bool(info["Flags"] == 1)
        self.device = info["Device"]
        # Monitor area (left, top, right, bottom)
        mon_rect = info["Monitor"]
        work_rect = info["Work"]
        self.monitor_area = mon_rect
        self.work_area = work_rect

    def get_dimensions(self):
        left, top, right, bottom = self.monitor_area
        return {"width": right - left, "height": bottom - top}

    def get_work_dimensions(self):
        """Get work area dimensions (excludes taskbar and other system UI)."""
        left, top, right, bottom = self.work_area
        return {"width": right - left, "height": bottom - top}

# ---------------------------------------------------------
# Existing Window, Monitor Classes
# ---------------------------------------------------------
class Window:
    def __init__(self, hwnd, process_name, config=None):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.process_name = process_name
        self.config = config or Config()
        self.type = self.config.get_type(process_name)

    def move_and_resize(self, x, y, width, height, restore_minimized=True, bring_to_front=False):
        """Position and optionally restore the window."""
        was_minimized = win32gui.IsIconic(self.hwnd)

        if restore_minimized and was_minimized:
            # SW_SHOWNORMAL is more reliable than SW_RESTORE for typical apps
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)

        # Move it
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

        # Bring to front if requested (apply to all windows when enabled, not just restored ones)
        if bring_to_front:
            self._force_window_to_front(x, y, width, height)
        else:
            # Standard positioning without stealing focus
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOP,
                x, y, width, height,
                win32con.SWP_NOSENDCHANGING | win32con.SWP_SHOWWINDOW
            )

    def _force_window_to_front(self, x, y, width, height):
        """Use multiple methods to force window to front of z-order."""
        try:
            # Method 1: Get current foreground window and thread
            current_fg = win32gui.GetForegroundWindow()
            current_thread = win32process.GetWindowThreadProcessId(current_fg)[0]
            target_thread = win32process.GetWindowThreadProcessId(self.hwnd)[0]

            # Method 2: Attach to foreground thread to bypass restrictions
            if current_thread != target_thread:
                win32process.AttachThreadInput(target_thread, current_thread, True)

            # Method 3: Temporarily make topmost, then remove topmost
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

            # Method 4: Set as foreground window
            win32gui.SetForegroundWindow(self.hwnd)

            # Method 5: Remove topmost status but keep it at the top
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_NOTOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

            # Method 6: Detach thread input
            if current_thread != target_thread:
                win32process.AttachThreadInput(target_thread, current_thread, False)

        except Exception:
            # Fallback: just use topmost toggle
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_NOTOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

    def __repr__(self):
        return f"<Window hwnd={self.hwnd} title='{self.title}' type={self.type}>"

class WindowManager:
    def __init__(self, config=None):
        self.windows = []
        self.config = config or Config()
        self._last_detection_time = 0
        self._cache_duration = 2.0  # Cache for 2 seconds
        self._window_cache = {}

    def detect_windows(self, force_refresh=False):
        """Detect windows with intelligent caching for performance."""
        import time
        current_time = time.time()
        
        # Use cache if it's still valid and not forcing refresh
        if not force_refresh and (current_time - self._last_detection_time) < self._cache_duration:
            # Validate cached windows still exist
            self._validate_cached_windows()
            return
        
        self.windows = []
        self._window_cache = {}

        def enum_callback(hwnd, _):
            # Check if window is visible and should be included
            if not self._is_window_visible_and_valid(hwnd):
                return True

            title = win32gui.GetWindowText(hwnd)
            if not title:
                return True

            try:
                _, pid = win32process.GetWindowThreadProcessId(hwnd)
                proc = psutil.Process(pid)
                process_name = os.path.basename(proc.exe())

                if self.config.should_skip(process_name):
                    return True

                # Cache window info for validation
                window_info = {
                    'hwnd': hwnd,
                    'title': title,
                    'process_name': process_name,
                    'pid': pid,
                    'detection_time': current_time
                }
                self._window_cache[hwnd] = window_info
                
                self.windows.append(Window(hwnd, process_name, self.config))
            except (psutil.NoSuchProcess, psutil.AccessDenied, OSError):
                # Skip windows we can't access
                pass
            except Exception:
                # Skip any other errors
                pass

            return True

        win32gui.EnumWindows(enum_callback, None)
        self._last_detection_time = current_time

    def _is_window_visible_and_valid(self, hwnd):
        """Check if a window is visible and should be included in tiling."""
        try:
            # Check if window exists and is visible
            if not win32gui.IsWindow(hwnd) or not win32gui.IsWindowVisible(hwnd):
                return False

            # Get window style and extended style
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
            ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

            # Skip windows that are:
            # - Tool windows (WS_EX_TOOLWINDOW)
            # - App windows (WS_EX_APPWINDOW) that are minimized
            # - Windows with no border or caption
            # - Windows that are child windows
            if (ex_style & win32con.WS_EX_TOOLWINDOW or
                (ex_style & win32con.WS_EX_APPWINDOW and style & win32con.WS_MINIMIZE) or
                not (style & win32con.WS_CAPTION) or
                style & win32con.WS_CHILD):
                return False

            # Get window rectangle
            try:
                rect = win32gui.GetWindowRect(hwnd)
                if not rect:
                    return False
                
                left, top, right, bottom = rect
                width = right - left
                height = bottom - top
                
                # Skip windows that are too small (likely system UI elements)
                if width < 50 or height < 50:
                    return False
                    
                # Skip windows that are positioned off-screen
                if right < 0 or bottom < 0 or left > 10000 or top > 10000:
                    return False
                    
            except Exception:
                return False

            # Get window title
            title = win32gui.GetWindowText(hwnd)
            if not title or title.strip() == "":
                return False

            # Skip windows with certain titles that indicate system UI
            skip_titles = {
                '', 'Program Manager', 'Microsoft Text Input Application',
                'Windows Security', 'Windows Defender', 'Windows Update',
                'Task Manager', 'Device Manager', 'Control Panel',
                'Settings', 'System Properties', 'Display Settings',
                'Personalization', 'Ease of Access', 'Privacy',
                'Update & Security', 'Apps & Features', 'Accounts',
                'Time & Language', 'Gaming', 'Search', 'Cortana'
            }
            
            if title.strip() in skip_titles:
                return False

            return True

        except Exception:
            return False

    def _validate_cached_windows(self):
        """Validate that cached windows still exist and are accessible."""
        valid_windows = []
        
        for window in self.windows:
            try:
                # Use the comprehensive visibility check
                if not self._is_window_visible_and_valid(window.hwnd):
                    continue
                
                # Check if process still exists
                _, pid = win32process.GetWindowThreadProcessId(window.hwnd)
                try:
                    proc = psutil.Process(pid)
                    # Verify process name hasn't changed
                    current_process_name = os.path.basename(proc.exe())
                    if current_process_name != window.process_name:
                        continue
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                
                # Window is still valid
                valid_windows.append(window)
                
            except Exception:
                # Window is no longer valid, skip it
                continue
        
        self.windows = valid_windows

    def get_windows_by_process(self):
        groups = {}
        for w in self.windows:
            groups.setdefault(w.process_name, []).append(w)
        return groups

    def get_windows_by_type(self):
        groups = {}
        for w in self.windows:
            groups.setdefault(w.type, []).append(w)
        return groups

    def get_all_windows_as_group(self):
        """Return all windows as a single group for 'tile all' functionality."""
        return {"All Windows": self.windows}

    def validate_windows_for_tiling(self, windows_to_tile):
        """Validate that windows are still accessible before tiling."""
        valid_windows = []
        
        for window in windows_to_tile:
            try:
                # Use the comprehensive visibility check
                if not self._is_window_visible_and_valid(window.hwnd):
                    continue
                
                # Check if process still exists
                _, pid = win32process.GetWindowThreadProcessId(window.hwnd)
                try:
                    proc = psutil.Process(pid)
                    # Verify process name hasn't changed
                    current_process_name = os.path.basename(proc.exe())
                    if current_process_name != window.process_name:
                        continue
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                
                valid_windows.append(window)
                
            except Exception:
                # Window is no longer valid, skip it
                continue
        
        return valid_windows


# ---------------------------------------------------------
# 2) MonitorManager
# ---------------------------------------------------------
class MonitorManager:
    """Responsible for discovering monitors and retrieving user-chosen monitors."""
    def __init__(self):
        self.monitors = []

    def detect_monitors(self):
        """Populate self.monitors with discovered Monitor objects."""
        self.monitors = []
        for (handle, _, info) in win32api.EnumDisplayMonitors(None, None):
            mon_info = win32api.GetMonitorInfo(handle)
            self.monitors.append(Monitor(handle, mon_info))

    def get_primary_monitor(self):
        """Return the primary monitor, or None if none found."""
        for m in self.monitors:
            if m.is_primary:
                return m
        return self.monitors[0] if self.monitors else None

    def get_monitor_by_index(self, index):
        """Return monitor by list index or None if out of range."""
        if 0 <= index < len(self.monitors):
            return self.monitors[index]
        return None

# ---------------------------------------------------------
# 3) LayoutManager
# ---------------------------------------------------------
class LayoutManager:
    """
    Responsible for applying different layout strategies (grid, custom, etc.).
    """
    def __init__(self):
        self.console = Console()

    def apply_grid_layout(self, windows, monitor, rows=2, cols=2, restore_minimized=True, bring_to_front=False, use_work_area=True):
        """Simple grid layout on the chosen monitor with window validation."""
        if not windows:
            self.console.print("No windows to tile.")
            return

        # Validate windows before tiling
        if hasattr(windows[0], 'hwnd'):  # Check if these are Window objects
            # This is a list of Window objects, validate them
            valid_windows = []
            for window in windows:
                try:
                    # Use comprehensive validation similar to WindowManager
                    if (win32gui.IsWindow(window.hwnd) and 
                        win32gui.IsWindowVisible(window.hwnd) and
                        self._is_window_valid_for_tiling(window.hwnd)):
                        valid_windows.append(window)
                except Exception:
                    continue
            windows = valid_windows
        else:
            # This might be a different format, try to handle it
            self.console.print("Warning: Unknown window format, proceeding with validation...")

        if not windows:
            self.console.print("No valid windows to tile after validation.")
            return

        # Use work area (excludes taskbar) by default, or full monitor area if requested
        if use_work_area:
            left, top, right, bottom = monitor.work_area
            area_type = "work area (taskbar-aware)"
        else:
            left, top, right, bottom = monitor.monitor_area
            area_type = "full monitor area"

        total_width = right - left
        total_height = bottom - top

        n = min(len(windows), rows * cols)

        successful_tiles = 0
        for i, w in enumerate(windows[:n]):
            try:
                row = i // cols
                col = i % cols

                x = left + int(col * (total_width / cols))
                y = top + int(row * (total_height / rows))
                wth = int(total_width / cols)
                hth = int(total_height / rows)

                # Final validation before moving window
                if (win32gui.IsWindow(w.hwnd) and 
                    win32gui.IsWindowVisible(w.hwnd) and
                    self._is_window_valid_for_tiling(w.hwnd)):
                    w.move_and_resize(x, y, wth, hth, restore_minimized=restore_minimized, bring_to_front=bring_to_front)
                    successful_tiles += 1
                else:
                    self.console.print(f"Warning: Window {w.title} is no longer valid, skipping...")
            except Exception as e:
                self.console.print(f"Error tiling window {w.title}: {e}")

        self.console.print(f"Successfully tiled {successful_tiles}/{n} windows in a {rows}x{cols} grid on {monitor.device} using {area_type}.")

    def _is_window_valid_for_tiling(self, hwnd):
        """Check if a window is valid for tiling operations."""
        try:
            # Check if window exists and is visible
            if not win32gui.IsWindow(hwnd) or not win32gui.IsWindowVisible(hwnd):
                return False

            # Get window style and extended style
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
            ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

            # Skip windows that are:
            # - Tool windows (WS_EX_TOOLWINDOW)
            # - App windows (WS_EX_APPWINDOW) that are minimized
            # - Windows with no border or caption
            # - Windows that are child windows
            if (ex_style & win32con.WS_EX_TOOLWINDOW or
                (ex_style & win32con.WS_EX_APPWINDOW and style & win32con.WS_MINIMIZE) or
                not (style & win32con.WS_CAPTION) or
                style & win32con.WS_CHILD):
                return False

            # Get window rectangle
            try:
                rect = win32gui.GetWindowRect(hwnd)
                if not rect:
                    return False
                
                left, top, right, bottom = rect
                width = right - left
                height = bottom - top
                
                # Skip windows that are too small (likely system UI elements)
                if width < 50 or height < 50:
                    return False
                    
                # Skip windows that are positioned off-screen
                if right < 0 or bottom < 0 or left > 10000 or top > 10000:
                    return False
                    
            except Exception:
                return False

            return True

        except Exception:
            return False

    def apply_custom_layout(self, windows, monitor, layout_config):
        """Example placeholder for custom layouts."""
        pass

    def create_layout_preset(self, name, config):
        """Register a named preset for future usage."""
        pass

# ---------------------------------------------------------
# 4) UserInterface
# ---------------------------------------------------------
class UserInterface:
    """Handles all user-facing input/output prompts using rich for better formatting."""

    def __init__(self):
        self.console = Console()

    def show_main_menu(self):
        """Display the main prompt menu and return user choice."""
        self.console.print("\n" + "="*60)
        self.console.print("           WINDOW TILER - PROMPT MODE")
        self.console.print("="*60)
        self.console.print("\nAvailable Commands:")
        self.console.print("1) Tile windows")
        self.console.print("2) List all windows")
        self.console.print("3) List monitors")
        self.console.print("4) Show help")
        self.console.print("5) Exit")
        self.console.print("-" * 30)

        choice = Prompt.ask("Select an option [1-5]", choices=["1", "2", "3", "4", "5"], default="1")
        return choice

    def show_help(self):
        """Display help information."""
        self.console.print("\n" + "="*50)
        self.console.print("                HELP")
        self.console.print("="*50)
        self.console.print("\nWindow Tiler allows you to automatically arrange windows in grids.")
        self.console.print("\nFeatures:")
        self.console.print("• Group windows by process name or type")
        self.console.print("• Manual window organization (export/edit/import)")
        self.console.print("• Customizable grid layouts (rows x columns)")
        self.console.print("• Multi-monitor support")
        self.console.print("• Restore minimized windows")
        self.console.print("• Taskbar-aware positioning")
        self.console.print("\nTips:")
        self.console.print("• Use 'Tile windows' to arrange your desktop")
        self.console.print("• Check 'List windows' to see what's available")
        self.console.print("• Use 'List monitors' to see available displays")
        self.console.print("• The application will keep running until you choose 'Exit'")

    def confirm_continue(self):
        """Ask user if they want to continue or return to main menu."""
        self.console.print("\n" + "-" * 40)
        return Confirm.ask("Return to main menu?", default=True)

    def show_monitor_selection(self, monitors):
        """Display available monitors in a simple format."""
        self.console.print("\nMonitors Detected:")
        for i, mon in enumerate(monitors):
            dims = mon.get_dimensions()
            work_dims = mon.get_work_dimensions()
            primary_txt = " [PRIMARY]" if mon.is_primary else ""
            self.console.print(f"{i + 1}) {mon.device} - {dims['width']}x{dims['height']} (work: {work_dims['width']}x{work_dims['height']}){primary_txt}")

    def prompt_monitor_index(self):
        return Prompt.ask("Select a monitor index", default="", show_default=False).strip()

    def show_grouped_windows(self, grouped):
        """Display grouped windows sorted by window count (descending)."""
        # Sort groups by window count (descending), then by name
        group_keys = sorted(grouped.keys(), key=lambda k: (-len(grouped[k]), str(k)))

        self.console.print("\nGroups Detected:")
        for idx, key in enumerate(group_keys):
            label = str(key)
            if isinstance(key, WindowType):
                label = key.name

            count = len(grouped[key])
            self.console.print(f"{idx + 1}) {label} -> {count} windows")

        return group_keys

    def get_user_choice_index(self):
        """Get user choice index with better error handling."""
        return Prompt.ask("\nWhich group do you want to tile? Enter index").strip()

    def get_layout_configuration(self, num_windows):
        """Prompt for rows, columns, and whether to restore minimized."""
        # Calculate dynamic defaults based on number of windows
        default_rows, default_cols = self._calculate_optimal_grid(num_windows)

        # Get rows first
        while True:
            rows_input = Prompt.ask(f"Number of rows", default=str(default_rows))
            try:
                rows = int(rows_input)
                if rows > 0:
                    break
                else:
                    self.console.print("Rows must be greater than 0. Please try again.")
            except ValueError:
                self.console.print("Invalid input. Please enter a valid number.")
                continue

        # Recalculate column default based on user's row choice
        adjusted_cols = math.ceil(num_windows / rows)

        # Get columns with the adjusted default
        while True:
            cols_input = Prompt.ask(f"Number of columns", default=str(adjusted_cols))
            try:
                cols = int(cols_input)
                if cols > 0:
                    break
                else:
                    self.console.print("Columns must be greater than 0. Please try again.")
            except ValueError:
                self.console.print("Invalid input. Please enter a valid number.")
                continue

        restore_minimized = Confirm.ask("Restore minimized windows?", default=True)

        # Only ask about bringing to front if restore is enabled
        bring_to_front = False
        if restore_minimized:
            bring_to_front = Confirm.ask("Force windows to foreground (aggressive activation)?", default=True)

        # Ask about taskbar-aware tiling
        use_work_area = Confirm.ask("Use taskbar-aware tiling (recommended)?", default=True)

        return rows, cols, restore_minimized, bring_to_front, use_work_area

    def prompt_window_organization(self, group_name="Windows"):
        """
        Prompt user whether to organize windows manually.

        Args:
            group_name: Name of the window group being organized

        Returns:
            Boolean indicating whether to use manual organization
        """
        if not ORGANIZER_AVAILABLE:
            return False

        return Confirm.ask(f"Organize {group_name} manually before tiling?", default=False)

    def _calculate_optimal_grid(self, num_windows):
        """Calculate optimal rows and columns for the given number of windows."""
        if num_windows <= 0:
            return 2, 2

        # For small numbers of windows, use simple layouts
        if num_windows == 1:
            return 1, 1
        elif num_windows == 2:
            return 1, 2
        elif num_windows == 3:
            return 1, 3
        elif num_windows == 4:
            return 2, 2
        elif num_windows <= 6:
            return 2, 3
        elif num_windows <= 8:
            return 2, 4
        elif num_windows == 9:
            return 3, 3
        elif num_windows <= 12:
            return 3, 4
        elif num_windows <= 15:
            return 3, 5
        elif num_windows == 16:
            return 4, 4
        else:
            # For larger numbers, try to create a roughly square grid
            # that can accommodate all windows
            sqrt_windows = math.sqrt(num_windows)
            rows = int(sqrt_windows)
            cols = math.ceil(num_windows / rows)

            # Ensure the grid can actually fit all windows
            while rows * cols < num_windows:
                if rows <= cols:
                    rows += 1
                else:
                    cols += 1

            return rows, cols

    def display_results(self, message):
        """Display results with simple formatting."""
        self.console.print(f"\n{message}")

    def display_error(self, message):
        """Display error messages."""
        self.console.print(f"\n{message}")

    def display_info(self, message):
        """Display info messages."""
        self.console.print(f"{message}")

    def show_header(self, title):
        """Display a simple header."""
        self.console.print(f"\n{title}")
        self.console.print("-" * len(title))

    def list_all_windows(self, windows):
        """Display a list of all detected windows."""
        if not windows:
            self.console.print("\nNo windows found!")
            return

        self.console.print(f"\nFound {len(windows)} windows:")
        self.console.print("-" * 60)

        for i, window in enumerate(windows, 1):
            title = window.title[:50] + "..." if len(window.title) > 50 else window.title
            self.console.print(f"{i:3d}. {window.process_name:20s} | {window.type:10s} | {title}")

    def list_monitors(self, monitors):
        """Display detailed monitor information."""
        if not monitors:
            self.console.print("\nNo monitors found!")
            return

        self.console.print(f"\nFound {len(monitors)} monitor(s):")
        self.console.print("-" * 80)

        for i, monitor in enumerate(monitors, 1):
            dims = monitor.get_dimensions()
            work_dims = monitor.get_work_dimensions()
            primary_txt = " [PRIMARY]" if monitor.is_primary else ""

            self.console.print(f"{i}. {monitor.device}{primary_txt}")
            self.console.print(f"   Full Area: {dims['width']} x {dims['height']}")
            self.console.print(f"   Work Area: {work_dims['width']} x {work_dims['height']}")
            self.console.print(f"   Monitor Rect: {monitor.monitor_area}")
            self.console.print(f"   Work Rect: {monitor.work_area}")
            if i < len(monitors):
                self.console.print()

# ---------------------------------------------------------
# 5) WindowTilerApp
# ---------------------------------------------------------
class WindowTilerApp:
    def __init__(self, config=None):
        self.config = config or Config()
        self.windowManager = WindowManager(self.config)
        self.monitorManager = MonitorManager()
        self.layoutManager = LayoutManager()
        self.ui = UserInterface()
        self.running = True

    def run(self):
        """Main prompt loop that runs continuously until user exits."""
        self.ui.console.print("\n🪟 Welcome to Window Tiler Prompt Mode!")
        self.ui.console.print("This session will continue until you explicitly choose to exit.")

        try:
            while self.running:
                try:
                    # Show main menu and get user choice
                    choice = self.ui.show_main_menu()

                    if choice == "1":
                        self._handle_tile_windows()
                    elif choice == "2":
                        self._handle_list_windows()
                    elif choice == "3":
                        self._handle_list_monitors()
                    elif choice == "4":
                        self._handle_show_help()
                    elif choice == "5":
                        self._handle_exit()
                        break

                    # After each operation (except exit), ask if user wants to continue
                    if self.running and choice != "5":
                        if not self.ui.confirm_continue():
                            self._handle_exit()
                            break

                except KeyboardInterrupt:
                    self.ui.console.print("\n\n⚠️  Ctrl+C detected. Use option 5 to exit properly.")
                    continue
                except Exception as e:
                    self.ui.display_error(f"An error occurred: {str(e)}")
                    self.ui.console.print("Returning to main menu...")
                    continue

        except KeyboardInterrupt:
            self.ui.console.print("\n\n👋 Session interrupted. Goodbye!")

        self.ui.console.print("\n✅ Window Tiler session ended.")

    def _handle_tile_windows(self):
        """Handle the tile windows workflow with improved validation."""
        self.ui.display_info("Finding windows...")

        self.windowManager.detect_windows()
        windows = self.windowManager.windows

        if not windows:
            self.ui.display_error("No windows found!")
            return

        self.ui.display_info(f"Total windows found: {len(windows)}")

        # Step: Choose grouping style
        while True:
            self.ui.console.print("\nChoose grouping style:")
            self.ui.console.print(f"1) Tile all {len(windows)} windows")
            self.ui.console.print("2) By process name (e.g. 'chrome.exe')")
            self.ui.console.print("3) By window type (BROWSER, TERMINAL, EDITOR, etc.)")
            choice = Prompt.ask("Enter [1/2/3]", choices=["1", "2", "3"], default="2")
            
            # Group windows
            if choice == "1":
                grouped = self.windowManager.get_all_windows_as_group()
                break
            elif choice == "2":
                grouped = self.windowManager.get_windows_by_process()
                break
            elif choice == "3":
                grouped = self.windowManager.get_windows_by_type()
                break
            else:
                self.ui.display_error("Invalid choice. Please enter 1, 2, or 3.")

        # Display group info
        group_keys = self.ui.show_grouped_windows(grouped)
        if not group_keys:
            self.ui.display_error("No groups found!")
            return

        # Step: Pick group
        while True:
            chosen = self.ui.get_user_choice_index()
            try:
                chosen_idx = int(chosen) - 1  # Convert from 1-based to 0-based indexing
                if chosen_idx < 0 or chosen_idx >= len(group_keys):
                    self.ui.display_error(f"Invalid choice. Please enter a number between 1 and {len(group_keys)}.")
                    continue
                group_key = group_keys[chosen_idx]
                break
            except (ValueError, IndexError):
                self.ui.display_error(f"Invalid choice. Please enter a number between 1 and {len(group_keys)}.")

        # Step: Get windows to tile
        windows_to_tile = grouped[group_key]
        group_name = str(group_key)

        # Step: Validate windows before proceeding
        self.ui.display_info(f"Validating {len(windows_to_tile)} windows...")
        valid_windows = self.windowManager.validate_windows_for_tiling(windows_to_tile)
        
        if len(valid_windows) != len(windows_to_tile):
            self.ui.console.print(f"⚠️  Warning: {len(windows_to_tile) - len(valid_windows)} windows are no longer valid and will be skipped.")
        
        if not valid_windows:
            self.ui.display_error("No valid windows to tile!")
            return

        windows_to_tile = valid_windows
        self.ui.display_info(f"Proceeding with {len(windows_to_tile)} valid windows.")

        # Step: Optional manual organization
        if ORGANIZER_AVAILABLE and len(windows_to_tile) > 1:
            organize_manually = self.ui.prompt_window_organization(group_name)
            if organize_manually:
                try:
                    # Export windows for manual editing
                    file_path = export_windows_for_editing(windows_to_tile, group_name)
                    self.ui.console.print(f"\n📝 Window organization table exported to:")
                    self.ui.console.print(f"   {file_path}")
                    self.ui.console.print("\n📋 The file has been opened in your default editor.")
                    self.ui.console.print("   • Reorder table rows to change window tiling order")
                    self.ui.console.print("   • First row = top-left position, second row = top-right, etc.")
                    self.ui.console.print("   • Save the file when done")

                    # Wait for user to edit and confirm
                    input("\n⏳ Press Enter after editing and saving the markdown file...")

                    # Import the edited order and validate again
                    windows_to_tile = import_edited_window_order(windows_to_tile)
                    windows_to_tile = self.windowManager.validate_windows_for_tiling(windows_to_tile)
                    self.ui.console.print(f"✅ Imported window order ({len(windows_to_tile)} windows)")

                    # Clean up the temporary file
                    cleanup_window_order_file()

                except Exception as e:
                    self.ui.display_error(f"Error during manual organization: {e}")
                    self.ui.console.print("Continuing with original window order...")

        # Step: Layout config
        rows, cols, restore_minimized, bring_to_front, use_work_area = self.ui.get_layout_configuration(len(windows_to_tile))

        # Step: Monitor selection
        self.monitorManager.detect_monitors()
        monitors = self.monitorManager.monitors
        if not monitors:
            self.ui.display_error("No monitors found!")
            return

        self.ui.show_monitor_selection(monitors)
        
        # Step: Monitor selection with validation
        while True:
            index_str = self.ui.prompt_monitor_index()
            
            if not index_str:
                monitor = self.monitorManager.get_primary_monitor()
                break
            else:
                try:
                    idx = int(index_str) - 1  # Convert from 1-based to 0-based indexing
                    monitor = self.monitorManager.get_monitor_by_index(idx)
                    if not monitor:
                        self.ui.display_error(f"Invalid monitor index. Please enter a number between 1 and {len(monitors)} or press Enter for primary monitor.")
                        continue
                    break
                except (ValueError, IndexError):
                    self.ui.display_error(f"Invalid monitor index. Please enter a number between 1 and {len(monitors)} or press Enter for primary monitor.")

        if not monitor:
            self.ui.display_error("No valid monitor found!")
            return

        # Step: Final validation before tiling
        self.ui.display_info("Performing final window validation...")
        final_windows = self.windowManager.validate_windows_for_tiling(windows_to_tile)
        
        if len(final_windows) != len(windows_to_tile):
            self.ui.console.print(f"⚠️  Warning: {len(windows_to_tile) - len(final_windows)} windows became invalid during setup and will be skipped.")
        
        if not final_windows:
            self.ui.display_error("No valid windows remaining for tiling!")
            return

        # Step: Do the layout
        self.layoutManager.apply_grid_layout(
            final_windows,
            monitor,
            rows,
            cols,
            restore_minimized=restore_minimized,
            bring_to_front=bring_to_front,
            use_work_area=use_work_area
        )

        self.ui.display_results("✅ Tiling completed successfully!")

    def _handle_list_windows(self):
        """Handle listing all windows with improved caching."""
        self.ui.display_info("Scanning for windows...")
        self.windowManager.detect_windows()  # This now uses caching
        self.ui.list_all_windows(self.windowManager.windows)

    def _handle_list_monitors(self):
        """Handle listing all monitors."""
        self.ui.display_info("Detecting monitors...")
        self.monitorManager.detect_monitors()
        self.ui.list_monitors(self.monitorManager.monitors)

    def _handle_show_help(self):
        """Handle showing help information."""
        self.ui.show_help()

    def _handle_exit(self):
        """Handle exit confirmation and cleanup."""
        if Confirm.ask("\n🚪 Are you sure you want to exit?", default=False):
            self.ui.console.print("👋 Thank you for using Window Tiler!")
            self.exit()
        else:
            self.ui.console.print("Continuing session...")

    def exit(self):
        """Clean up resources if needed and exit."""
        self.running = False

# ---------------------------------------------------------
# 6) Entry Point
# ---------------------------------------------------------
def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Window Tiler - Automatically arrange windows in grids",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python window_tiler.py                    # Run in prompt mode (default)
  python window_tiler.py --prompt           # Explicitly run in prompt mode
  python window_tiler.py --once             # Run once and exit (legacy mode)

Prompt mode keeps the application running until you explicitly exit,
allowing you to perform multiple tiling operations in a single session.
        """
    )

    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '--prompt', '-p',
        action='store_true',
        default=True,
        help='Run in prompt mode with persistent session (default)'
    )
    mode_group.add_argument(
        '--once', '-o',
        action='store_true',
        help='Run once and exit (legacy behavior)'
    )

    parser.add_argument(
        '--version', '-v',
        action='version',
        version='Window Tiler 2.0 - Prompt Edition'
    )

    return parser.parse_args()

def main():
    """Main entry point with argument parsing."""
    args = parse_arguments()

    # Create the app
    app = WindowTilerApp()

    if args.once:
        # Legacy mode: run once and exit
        app._handle_tile_windows()
    else:
        # Default: prompt mode
        app.run()

if __name__ == "__main__":
    main()
